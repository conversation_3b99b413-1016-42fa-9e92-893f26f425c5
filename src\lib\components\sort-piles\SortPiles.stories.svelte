<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import SortPiles from '$lib/components/sort-piles/SortPiles.svelte';

	const { Story } = defineMeta({
		title: 'Components/SortPiles/SortPiles',
		component: SortPiles,
		argTypes: {
			pileNameToCards: { control: 'object' },
			sortPilesConfiguration: { control: 'object' },
			cardConfiguration: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

<Story
	name="Primary"
	args={{
		pileNameToCards: new Map(),
		sortPilesConfiguration: { pileNames: [] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithOnePile"
	args={{
		pileNameToCards: new Map([['Pile 1', []]]),
		sortPilesConfiguration: { pileNames: ['Pile 1'] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithThreePiles"
	args={{
		pileNameToCards: new Map([
			['Pile 1', []],
			['Pile 2', []],
			['Pile 3', []]
		]),
		sortPilesConfiguration: { pileNames: ['Pile 1', 'Pile 2', 'Pile 3'] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithACard"
	args={{
		pileNameToCards: new Map([
			['Pile 1', []],
			['Pile 2', []],
			['Pile 3', []]
		]),
		sortPilesConfiguration: { pileNames: ['Pile 1', 'Pile 2', 'Pile 3'] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
	play={async ({ context }) => {
		const pileNameToCards = context.args.pileNameToCards;
		pileNameToCards.get('Pile 1')!.push({
			id: 1,
			frontSide: {
				content: {
					multiText: {
						title: { text: 'Title 1' },
						body: { text: 'Body 1' },
						footer: { text: 'Footer 1' }
					}
				}
			},
			backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } },
			isFrontVisible: true
		});
	}}
/>

<Story
	name="WithDropHighlight"
	args={{
		pileNameToCards: new Map([
			['Pile 1', []],
			['Pile 2', []],
			['Pile 3', []]
		]),
		sortPilesConfiguration: { pileNames: ['Pile 1', 'Pile 2', 'Pile 3'] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
	play={async ({ canvasElement }) => {
		const dropTargetEl = canvasElement.querySelector('.card-drop-target');
		dropTargetEl!.classList.add('drop-target');
	}}
/>

<Story
	name="WithDropHighlightOverCard"
	args={{
		pileNameToCards: new Map([
			['Pile 1', []],
			['Pile 2', []],
			['Pile 3', []]
		]),
		sortPilesConfiguration: { pileNames: ['Pile 1', 'Pile 2', 'Pile 3'] },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
	play={async ({ canvasElement, context }) => {
		const pileNameToCards = context.args.pileNameToCards;
		pileNameToCards.get('Pile 1')!.push({
			id: 1,
			frontSide: {
				content: {
					multiText: {
						title: { text: 'Title 1' },
						body: { text: 'Body 1' },
						footer: { text: 'Footer 1' }
					}
				}
			},
			backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } },
			isFrontVisible: true
		});
		const dropTargetEl = canvasElement.querySelector('.card-drop-target');
		dropTargetEl!.classList.add('drop-target');
	}}
/>
