<script lang="ts">
	import Card from '$lib/components/card/Card.svelte';
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import type { DeckConfiguration } from '$lib/models/deckConfiguration';
	import type { Card as CardModel } from '$lib/models/card';
	import CardContainer from '$lib/components/card/CardContainer.svelte';
	import { sendRotatedCard } from '$lib/transitions/crossfadeCard';
	import { sineIn } from 'svelte/easing';

	interface DeckProps {
		cards: CardModel[];
		canClickDeck?: boolean;
		deckConfiguration: DeckConfiguration;
		cardConfiguration: CardConfiguration;
		onClickDeck?: () => void;
	}

	let {
		cards,
		canClickDeck = true,
		deckConfiguration,
		cardConfiguration,
		onClickDeck
	}: DeckProps = $props();

	let visibleCards = $derived(cards?.slice(0, deckConfiguration.numVisibleCards).reverse());
</script>

<CardContainer widthPx={cardConfiguration.widthPx} aspectRatio={cardConfiguration.aspectRatio}>
	<div class="grid grid-cols-1 grid-rows-1">
		{#each visibleCards as card, i (card.id)}
			<button
				class="card col-start-1 row-start-1 w-min transform-gpu {canClickDeck
					? 'cursor-pointer'
					: 'cursor-not-allowed'}"
				style="transform: rotate({deckConfiguration.initialCardAngleDeg +
					i * deckConfiguration.cardAngleDegStep}deg);"
				onclick={() => canClickDeck && onClickDeck?.()}
				out:sendRotatedCard={{ key: card.id, duration: 600, easing: sineIn }}
			>
				<Card {...card} {cardConfiguration} />
			</button>
		{/each}
	</div>
</CardContainer>

<style>
	.card {
		transform-origin: 80% 100% 0;
		transform: translate(0);
		transition: transform 0.6s cubic-bezier(0.12, 0, 0.39, 0);
	}
</style>
