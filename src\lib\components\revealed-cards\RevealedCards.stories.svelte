<script module>
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import RevealedCards from '$lib/components/revealed-cards/RevealedCards.svelte';

	const { Story } = defineMeta({
		title: 'Components/RevealedCards',
		component: RevealedCards,
		argTypes: {
			cards: { control: 'object' },
			revealedCardsConfiguration: { control: 'object' },
			cardConfiguration: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setRevealedCardsState } from '$lib/states/revealedCardsState.svelte';
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let revealedCardsState = setRevealedCardsState();
	let gameConfigurationState = setGameConfigurationState();
</script>

<Story
	name="Primary"
	args={{
		cards: revealedCardsState.cards,
		revealedCardsConfiguration: gameConfigurationState.revealedCardsConfiguration,
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithThreeRevealSpots"
	args={{
		cards: revealedCardsState.cards,
		revealedCardsConfiguration: Object.assign(
			{},
			gameConfigurationState.revealedCardsConfiguration,
			{
				numRevealSpots: 3
			}
		),
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithOneRevealedCard"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		revealedCardsConfiguration: Object.assign(
			{},
			gameConfigurationState.revealedCardsConfiguration,
			{
				numRevealSpots: 3
			}
		),
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>
