<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Deck from '$lib/components/deck/Deck.svelte';

	const { Story } = defineMeta({
		title: 'Components/Deck/Deck',
		component: Deck,
		argTypes: {
			cards: { control: 'object' },
			canClickDeck: { control: 'boolean' },
			deckConfiguration: { control: 'object' },
			cardConfiguration: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setDeckState } from '$lib/states/deckState.svelte';
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let deckState = setDeckState();
	let gameConfigurationState = setGameConfigurationState();
</script>

<Story
	name="Primary"
	args={{
		cards: deckState.cards,
		deckConfiguration: gameConfigurationState.deckConfiguration,
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithFourVisibleCards"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 2' },
							body: { text: 'Body 2' },
							footer: { text: 'Footer 2' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 3,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 3' },
							body: { text: 'Body 3' },
							footer: { text: 'Footer 3' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 4,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 4' },
							body: { text: 'Body 4' },
							footer: { text: 'Footer 4' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		deckConfiguration: Object.assign({}, gameConfigurationState.deckConfiguration, {
			numVisibleCards: 4
		}),
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithTwoVisibleCards"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 2' },
							body: { text: 'Body 2' },
							footer: { text: 'Footer 2' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		deckConfiguration: Object.assign({}, gameConfigurationState.deckConfiguration, {
			numVisibleCards: 2
		}),
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithDisabledClick"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 2' },
							body: { text: 'Body 2' },
							footer: { text: 'Footer 2' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 3,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 3' },
							body: { text: 'Body 3' },
							footer: { text: 'Footer 3' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 4,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 4' },
							body: { text: 'Body 4' },
							footer: { text: 'Footer 4' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		canClickDeck: false,
		deckConfiguration: gameConfigurationState.deckConfiguration,
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>
