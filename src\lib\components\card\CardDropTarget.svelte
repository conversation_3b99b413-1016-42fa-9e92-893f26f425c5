<script lang="ts">
	import type { Card } from '$lib/models/card';
	import type { CornerRadius } from '$lib/models/cardConfiguration';

	interface CardDropTargetProps {
		cornerRadius?: CornerRadius;
		onDroppedTo?: (card: Card) => void;
	}

	let { cornerRadius, onDroppedTo }: CardDropTargetProps = $props();
</script>

<div
	class="card-drop-target absolute inset-0 bg-blue-500 opacity-0 {cornerRadius
		? `rounded-${cornerRadius}`
		: ''}"
	ondroppedto={(event: CustomEvent<Card>) => {
		onDroppedTo?.(event.detail);
	}}
></div>

<style>
	.card-drop-target.drop-target {
		opacity: 0.3;
	}
</style>
