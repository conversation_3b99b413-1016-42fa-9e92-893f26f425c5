<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import CardDropTarget from '$lib/components/card/CardDropTarget.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';

	const { Story } = defineMeta({
		title: 'Components/Card/CardDropTarget',
		component: CardDropTarget,
		argTypes: {
			cornerRadius: { control: 'text' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

{#snippet template(args: any)}
	<CardContainer class="relative" {...gameConfigurationState.cardConfiguration}>
		<CardDropTarget {...args} />
	</CardContainer>
{/snippet}

<Story
	name="Primary"
	args={{ cornerRadius: gameConfigurationState.cardConfiguration.cornerRadius }}
	{template}
/>

<Story
	name="WithDropHighlight"
	args={{ cornerRadius: gameConfigurationState.cardConfiguration.cornerRadius }}
	{template}
	play={async ({ canvasElement }) => {
		const dropTargetEl = canvasElement.querySelector('.card-drop-target');
		dropTargetEl!.classList.add('drop-target');
	}}
/>
