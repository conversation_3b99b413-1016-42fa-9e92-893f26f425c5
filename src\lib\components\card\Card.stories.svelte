<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Card from '$lib/components/card/Card.svelte';

	const { Story } = defineMeta({
		title: 'Components/Card/Card',
		component: Card,
		argTypes: {
			backSide: { control: 'object' },
			frontSide: { control: 'object' },
			isFrontVisible: { control: 'boolean' },
			cardConfiguration: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

<Story name="Primary" args={{ cardConfiguration: gameConfigurationState.cardConfiguration }} />

<Story
	name="WithMultiTextAndBackImage"
	args={{
		frontSide: {
			content: {
				multiText: { title: { text: 'Title' }, body: { text: 'Body' }, footer: { text: 'Footer' } }
			}
		},
		backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } },
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithSquareCorners"
	args={{
		cardConfiguration: Object.assign({}, gameConfigurationState.cardConfiguration, {
			cornerRadius: 'none'
		})
	}}
/>
