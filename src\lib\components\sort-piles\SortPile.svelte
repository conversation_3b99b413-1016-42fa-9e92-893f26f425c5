<script lang="ts">
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import Card from '$lib/components/card/Card.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';
	import type { Card as CardModel } from '$lib/models/card';
	import { sineIn } from 'svelte/easing';
	import { receiveCard } from '$lib/transitions/crossfadeCard';
	import CardPlaceholder from '../card/CardPlaceholder.svelte';
	import CardDropTarget from '../card/CardDropTarget.svelte';

	interface SortPileProps {
		pileName: string;
		cards: CardModel[];
		cardConfiguration: CardConfiguration;
		onDroppedTo?: (card: CardModel, pileName: string) => void;
	}

	let { pileName, cards, cardConfiguration, onDroppedTo }: SortPileProps = $props();

	let topTwoCards = $derived(cards.slice(0, 2).reverse());
</script>

<div class="sort-pile w-min">
	<CardContainer class="relative" {...cardConfiguration}>
		<CardPlaceholder {...cardConfiguration} />
		{#each topTwoCards as card (card.id)}
			<div class="absolute inset-0" in:receiveCard={{ key: card.id, easing: sineIn }}>
				<Card {...card} {cardConfiguration} />
			</div>
		{/each}
		<CardDropTarget {...cardConfiguration} onDroppedTo={(card) => onDroppedTo?.(card, pileName)}
		></CardDropTarget>
	</CardContainer>
	<p class="text-center text-lg font-medium">{pileName}</p>
</div>
