<script lang="ts">
	import SortItBoard from '$lib/components/game-board/SortItBoard.svelte';
	import type { GameConfiguration } from '$lib/models/gameConfiguration';
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import { cards } from '$lib/data/cards.json';
	import {
		cardConfiguration,
		revealedCardsConfiguration,
		deckConfiguration,
		sortPilesConfiguration
	} from '$lib/data/game.json';

	let gameConfiguration: GameConfiguration = {
		// Casting to avoid TS error because we can't be sure that cornerRadius is of correct value
		// since we're using our own JSON data it's safe to do so.
		cardConfiguration: cardConfiguration as CardConfiguration,
		revealedCardsConfiguration,
		deckConfiguration,
		sortPilesConfiguration
	};
</script>

<div class="h-full w-full p-16">
	<SortItBoard {cards} {gameConfiguration} />
</div>
