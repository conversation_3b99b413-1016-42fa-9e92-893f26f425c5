<script module>
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import CardSide from '$lib/components/card/CardSide.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';

	const { Story } = defineMeta({
		title: 'Components/Card/CardSide',
		component: CardSide,
		argTypes: {
			backgroundImageUrl: { control: 'text' },
			content: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

{#snippet template(args: any)}
	<CardContainer
		widthPx={gameConfigurationState.cardConfiguration.widthPx}
		aspectRatio={gameConfigurationState.cardConfiguration.aspectRatio}
	>
		<CardSide {...args} />
	</CardContainer>
{/snippet}

<Story name="Primary" args={{}} {template} />

<Story
	name="WithMultiText"
	args={{
		content: {
			multiText: {
				title: { text: 'Title' },
				body: { text: 'Body' },
				footer: { text: 'Footer' }
			}
		}
	}}
	{template}
/>

<Story
	name="WithOverflowingTitleInMultiText"
	args={{
		content: {
			multiText: {
				title: {
					text: 'Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title'
				}
			}
		}
	}}
	{template}
/>

<Story
	name="WithOverflowingBodyInMultiText"
	args={{
		content: {
			multiText: {
				body: {
					text: 'Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body'
				}
			}
		}
	}}
	{template}
/>

<Story
	name="WithOverflowingFooterInMultiText"
	args={{
		content: {
			multiText: {
				footer: {
					text: 'Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer'
				}
			}
		}
	}}
	{template}
/>

<Story
	name="WithOverflowingEverythingInMultiText"
	args={{
		content: {
			multiText: {
				title: {
					text: 'Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title Title'
				},
				body: {
					text: 'Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body Body'
				},
				footer: {
					text: 'Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer Footer'
				}
			}
		}
	}}
	{template}
/>

<Story name="WithBigText" args={{ content: { bigText: { text: 'Big Text' } } }} {template} />

<Story
	name="WithOverflowingBigText"
	args={{
		content: {
			bigText: {
				text: 'Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text Big Text'
			}
		}
	}}
	{template}
/>

<Story
	name="WithSquareImage"
	args={{ content: { image: { url: 'https://picsum.photos/200/200' } } }}
	{template}
/>

<Story
	name="WithTallImage"
	args={{ content: { image: { url: 'https://picsum.photos/200/800' } } }}
	{template}
/>

<Story
	name="WithWideImage"
	args={{ content: { image: { url: 'https://picsum.photos/800/200' } } }}
	{template}
/>

<Story
	name="WithVideo"
	args={{
		content: {
			video: { url: 'https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4' }
		}
	}}
	{template}
/>

<Story
	name="WithAudio"
	args={{
		content: {
			audio: { url: 'https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4' }
		}
	}}
	{template}
/>
