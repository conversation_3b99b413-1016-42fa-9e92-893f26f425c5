import { cubicOut, sineIn } from 'svelte/easing';
import { crossfade, fly, type CrossfadeParams, type TransitionConfig } from 'svelte/transition';

export const [sendCard, receiveCard] = crossfade({
	duration: (d) => Math.sqrt(d * 200),
	fallback: (node) => fly(node, { duration: 600, x: 300, easing: sineIn })
});

export const [sendRotatedCard, receiveRotatedCard] = crossfadeCard({
	duration: (d) => Math.sqrt(d * 200)
});

type CrossfadeCardParams = CrossfadeParams & {
	fallback?: (node: Element, params: CrossfadeParams, intro: boolean) => TransitionConfig;
};

type CrossFadeCardReturn = [
	(node: Element, params: CrossfadeParams & { key: unknown }) => () => TransitionConfig,
	(node: Element, params: CrossfadeParams & { key: unknown }) => () => TransitionConfig
];

// Modified version of svelte's crossfade to support card rotation
export function crossfadeCard({ fallback, ...defaults }: CrossfadeCardParams): CrossFadeCardReturn {
	const to_receive: Map<unknown, Element> = new Map();
	const to_send: Map<unknown, Element> = new Map();

	function crossfade(
		from_node: Element,
		to_node: Element,
		params: CrossfadeParams
	): TransitionConfig {
		const {
			delay = 0,
			duration = (d: number) => Math.sqrt(d) * 30,
			easing = cubicOut
		} = Object.assign({}, defaults, params);
		// Grab from/to bounding rects from the parent elements, since they shouldn't be rotated
		// WARNING: Parent element has to be exactly the same size and position as the unrotated card
		const from =
			from_node.parentElement?.getBoundingClientRect() ?? from_node.getBoundingClientRect();
		const to = to_node.parentElement?.getBoundingClientRect() ?? to_node.getBoundingClientRect();
		const dx = from.left - to.left;
		const dy = from.top - to.top;
		const d = Math.sqrt(dx * dx + dy * dy);
		const style = getComputedStyle(to_node);

		const opacity = +style.opacity;
		const fromRotation = extractRotation(from_node);
		const toRotation = extractRotation(to_node);
		const rotationDiff = fromRotation - toRotation;

		// Handle transform interpolation based on type
		const transformCSS = getTransformInterpolation(to_node, (u) => ({
			translate: { x: u * dx, y: u * dy },
			rotate: u * rotationDiff
		}));

		return {
			delay,
			duration: typeof duration === 'function' ? duration(d) : duration,
			easing,
			css: (t, u) => `
				opacity: ${t * opacity};
				transform-origin: 80% 100% 0;
				${transformCSS(u)}
			`
		};
	}

	function transition(
		items: Map<unknown, Element>,
		counterparts: Map<unknown, Element>,
		intro: boolean
	) {
		// @ts-expect-error TODO improve typings (are the public types wrong?)
		return (node, params) => {
			items.set(params.key, node);
			return () => {
				if (counterparts.has(params.key)) {
					const other_node = counterparts.get(params.key) as Element;
					counterparts.delete(params.key);
					return crossfade(other_node, node, params);
				}
				// if the node is disappearing altogether
				// (i.e. wasn't claimed by the other list)
				// then we need to supply an outro
				items.delete(params.key);
				return fallback && fallback(node, params, intro);
			};
		};
	}

	// @ts-expect-error TODO improve svelte typings
	return [transition(to_send, to_receive, false), transition(to_receive, to_send, true)];
}

interface TransformMatch {
	type: 'rotate' | 'matrix' | 'matrix3d';
	values: number[];
}

function parseTransform(transform: string): TransformMatch | null {
	if (transform === 'none') return null;

	const rotateMatch = transform.match(/rotate\(([^)]+)deg\)/);
	if (rotateMatch && rotateMatch[1]) {
		return { type: 'rotate', values: [parseFloat(rotateMatch[1])] };
	}

	const matrixMatch = transform.match(/matrix\(([^)]+)\)/);
	if (matrixMatch && matrixMatch[1]) {
		const values = matrixMatch[1].split(',').map((v) => parseFloat(v.trim()));
		return { type: 'matrix', values };
	}

	const matrix3dMatch = transform.match(/matrix3d\(([^)]+)\)/);
	if (matrix3dMatch && matrix3dMatch[1]) {
		const values = matrix3dMatch[1].split(',').map((v) => parseFloat(v.trim()));
		return { type: 'matrix3d', values };
	}

	return null;
}

function extractRotationFromMatrix(values: number[]): number {
	if (values.length >= 4) {
		const angleRad = Math.atan2(values[1], values[0]);
		return angleRad * (180 / Math.PI);
	}
	return 0;
}

function extractRotation(node: Element): number {
	const style = getComputedStyle(node);
	const transformMatch = parseTransform(style.transform);

	if (!transformMatch) return 0;

	switch (transformMatch.type) {
		case 'rotate':
			return transformMatch.values[0];
		case 'matrix':
		case 'matrix3d':
			return extractRotationFromMatrix(transformMatch.values);
		default:
			return 0;
	}
}

// Helper function to generate transform CSS based on the original transform type
function getTransformInterpolation(
	node: Element,
	getValues: (u: number) => { translate: { x: number; y: number }; rotate: number }
): (u: number) => string {
	const style = getComputedStyle(node);
	const transformMatch = parseTransform(style.transform);

	if (!transformMatch) {
		// Simple case: no existing transform
		return (u) => {
			const values = getValues(u);
			return `transform: translate(${values.translate.x}px, ${values.translate.y}px) rotate(${values.rotate}deg);`;
		};
	}

	switch (transformMatch.type) {
		case 'matrix':
			return (u) => {
				const values = getValues(u);
				const newMatrix = combineMatrixWithTransform(
					transformMatch.values,
					values.translate,
					values.rotate
				);
				return `transform: matrix(${newMatrix.join(', ')});`;
			};

		case 'matrix3d':
			return (u) => {
				const values = getValues(u);
				const newMatrix = combineMatrix3dWithTransform(
					transformMatch.values,
					values.translate,
					values.rotate
				);
				return `transform: matrix3d(${newMatrix.join(', ')});`;
			};

		case 'rotate':
			return (u) => {
				const values = getValues(u);
				return `transform: translate(${values.translate.x}px, ${values.translate.y}px) rotate(${transformMatch.values[0] + values.rotate}deg);`;
			};

		default:
			// Fallback: append our transforms to the original transform string
			return (u) => {
				const values = getValues(u);
				return `transform: ${style.transform} translate(${values.translate.x}px, ${values.translate.y}px) rotate(${values.rotate}deg);`;
			};
	}
}

// Shared rotation utilities
interface RotationValues {
	cos: number;
	sin: number;
	radians: number;
}

function getRotationValues(degrees: number): RotationValues {
	const radians = degrees * (Math.PI / 180);
	return {
		cos: Math.cos(radians),
		sin: Math.sin(radians),
		radians
	};
}

// Helper function to combine a 2D matrix with translation and rotation
function combineMatrixWithTransform(
	matrix: number[],
	translate: { x: number; y: number },
	rotateDeg: number
): number[] {
	const rotation = getRotationValues(rotateDeg);

	// Create rotation matrix
	const rotationMatrix = [
		rotation.cos,
		rotation.sin,
		-rotation.sin,
		rotation.cos,
		translate.x,
		translate.y
	];

	// Combine matrices (simplified approach)
	return [
		matrix[0] * rotationMatrix[0] + matrix[2] * rotationMatrix[1],
		matrix[1] * rotationMatrix[0] + matrix[3] * rotationMatrix[1],
		matrix[0] * rotationMatrix[2] + matrix[2] * rotationMatrix[3],
		matrix[1] * rotationMatrix[2] + matrix[3] * rotationMatrix[3],
		matrix[4] + translate.x,
		matrix[5] + translate.y
	];
}

// Helper function to combine a 3D matrix with translation and rotation (z-axis)
function combineMatrix3dWithTransform(
	matrix: number[],
	translate: { x: number; y: number },
	rotateDeg: number
): number[] {
	const rotation = getRotationValues(rotateDeg);

	// Create rotation matrix for z-axis rotation in 3D space
	// In column-major format as used by CSS matrix3d
	const rotationMatrix = [
		rotation.cos, // m11
		rotation.sin, // m12
		0, // m13
		0, // m14
		-rotation.sin, // m21
		rotation.cos, // m22
		0, // m23
		0, // m24
		0, // m31
		0, // m32
		1, // m33
		0, // m34
		translate.x, // m41
		translate.y, // m42
		0, // m43
		1 // m44
	];

	// Combine the matrices using matrix multiplication
	return multiply4x4Matrices(matrix, rotationMatrix);
}

// Helper function to multiply two 4x4 matrices (column-major format)
function multiply4x4Matrices(a: number[], b: number[]): number[] {
	const result = new Array(16);

	for (let row = 0; row < 4; row++) {
		for (let col = 0; col < 4; col++) {
			const resultIndex = col * 4 + row;
			result[resultIndex] =
				a[row] * b[col * 4] +
				a[row + 4] * b[col * 4 + 1] +
				a[row + 8] * b[col * 4 + 2] +
				a[row + 12] * b[col * 4 + 3];
		}
	}

	return result;
}
