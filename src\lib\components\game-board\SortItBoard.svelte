<script lang="ts">
	import type { Card } from '$lib/models/card';
	import { setDeckState } from '$lib/states/deckState.svelte';
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';
	import { setRevealedCardsState } from '$lib/states/revealedCardsState.svelte';
	import Deck from '$lib/components/deck/Deck.svelte';
	import RevealedCards from '$lib/components/revealed-cards/RevealedCards.svelte';
	import { tick } from 'svelte';
	import { throttle } from '$lib/utils/throttle';
	import type { GameConfiguration } from '$lib/models/gameConfiguration';
	import SortPiles from '../sort-piles/SortPiles.svelte';
	import { setSortPilesState } from '$lib/states/sortPilesState.svelte';

	interface SortItBoardProps {
		cards: Card[];
		gameConfiguration?: GameConfiguration;
	}

	let { cards, gameConfiguration }: SortItBoardProps = $props();

	let gameConfigurationState = setGameConfigurationState(gameConfiguration);
	let deckState = setDeckState(cards);
	let revealedCardsState = setRevealedCardsState();
	let sortPilesState = setSortPilesState(gameConfigurationState.sortPilesConfiguration);

	let pileNameToCards = $derived(
		new Map(
			sortPilesState.pileNameToCards
				.keys()
				.map((pileName) => [pileName, sortPilesState.pileNameToCards.get(pileName)!.cards])
		)
	);

	async function revealDeckCard(card: Card) {
		// Clone the card so that we can set isFrontVisible twice
		// allowing us to trigger the flip animation twice, once
		// for the outro deck card and once for the intro revealed card
		const cardCopy = $state({ ...card });

		// Handle the outro animation for the deck card
		card.isFrontVisible = true;
		// Tick needed to trigger the flip animation on the outro deck card
		// Tick assures that the DOM is updated with `isFrontVisible` before
		// we remove the card from the deck
		await tick();
		deckState.remove(card);

		// Handle the intro animation for the revealed card
		revealedCardsState.addOnTop(cardCopy);
		// Tick needed to trigger the flip animation on the intro revealed card
		// Tick assures that the new revealed card is rendered in the DOM before we
		// set `isFrontVisible` to true
		await tick();
		cardCopy.isFrontVisible = true;
	}

	function ensureNoExcessRevealedCard() {
		// Move the bottom revealed card back to the deck if we have more cards than reveal spots
		if (
			revealedCardsState.cards.length >
			gameConfigurationState.revealedCardsConfiguration.numRevealSpots
		) {
			returnBottomRevealedCardToDeck();
		}
	}

	function returnBottomRevealedCardToDeck() {
		const cardToMoveBackToDeck = revealedCardsState.removeFromBottom();
		cardToMoveBackToDeck!.isFrontVisible = false;
		deckState.addAtBottom(cardToMoveBackToDeck!);
	}

	async function handleClickDeck() {
		const topCard = deckState.topCard;
		if (!topCard) {
			return;
		}

		await revealDeckCard(topCard);

		ensureNoExcessRevealedCard();
	}

	function handleDroppedToPile(card: Card, pileName: string) {
		revealedCardsState.remove(card);
		sortPilesState.pileNameToCards.get(pileName)!.addOnTop(card);
	}
</script>

<div class="flex w-full flex-col items-center gap-48">
	<div class="z-10 flex gap-96">
		<Deck
			cards={deckState.cards}
			deckConfiguration={gameConfigurationState.deckConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
			onClickDeck={throttle(handleClickDeck, 700)}
		/>

		<RevealedCards
			cards={revealedCardsState.cards}
			revealedCardsConfiguration={gameConfigurationState.revealedCardsConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
		/>
	</div>
	<div>
		<SortPiles
			{pileNameToCards}
			sortPilesConfiguration={gameConfigurationState.sortPilesConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
			onDroppedToPile={handleDroppedToPile}
		/>
	</div>
</div>
