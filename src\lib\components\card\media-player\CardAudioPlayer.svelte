<script lang="ts">
	import 'media-chrome';

	let {
		url,
		preload = 'metadata'
	}: { url: string; preload?: '' | 'none' | 'metadata' | 'auto' | null | undefined } = $props();

	let audioEl: HTMLAudioElement;

	function handleClick() {
		audioEl.paused ? audioEl.play() : audioEl.pause();
	}
</script>

<button class="h-full w-full" onclick={handleClick} aria-label="Play audio">
	<media-controller audio class="w-full">
		<audio slot="media" src={url} {preload} bind:this={audioEl}></audio>
		<media-control-bar class="flex">
			<media-play-button></media-play-button>
			<div class="grow"></div>
			<media-time-display showduration></media-time-display>
			<div class="grow"></div>
			<media-mute-button></media-mute-button>
		</media-control-bar>
	</media-controller>
</button>
