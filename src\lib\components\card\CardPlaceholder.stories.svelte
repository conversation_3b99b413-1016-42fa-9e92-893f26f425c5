<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import CardPlaceholder from '$lib/components/card/CardPlaceholder.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';

	const { Story } = defineMeta({
		title: 'Components/Card/CardPlaceholder',
		component: CardPlaceholder,
		argTypes: {
			cornerRadius: { control: 'text' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

{#snippet template(args: any)}
	<CardContainer {...gameConfigurationState.cardConfiguration}>
		<CardPlaceholder {...args} />
	</CardContainer>
{/snippet}

<Story
	name="Primary"
	args={{ cornerRadius: gameConfigurationState.cardConfiguration.cornerRadius }}
	{template}
/>
