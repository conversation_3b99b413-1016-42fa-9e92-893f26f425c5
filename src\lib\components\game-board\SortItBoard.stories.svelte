<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import SortItBoard from '$lib/components/game-board/SortItBoard.svelte';
	import { sleep } from '$lib/utils/sleep';

	const { Story } = defineMeta({
		title: 'Components/GameBoard/SortItBoard',
		component: SortItBoard,
		argTypes: { cards: { control: 'object' }, gameConfiguration: { control: 'object' } }
	});
</script>

<Story name="Primary" />
<Story
	name="WithOneCardInDeck"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		]
	}}
/>

<Story
	name="WithFourCardsInDeckAndThreeRevealSpots"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 2' },
							body: { text: 'Body 2' },
							footer: { text: 'Footer 2' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 3,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 3' },
							body: { text: 'Body 3' },
							footer: { text: 'Footer 3' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 4,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 4' },
							body: { text: 'Body 4' },
							footer: { text: 'Footer 4' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		gameConfiguration: {
			revealedCardsConfiguration: {
				numRevealSpots: 3
			}
		}
	}}
/>

<Story
	name="WithDeckConstantlyRevealing"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 2' },
							body: { text: 'Body 2' },
							footer: { text: 'Footer 2' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 3,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 3' },
							body: { text: 'Body 3' },
							footer: { text: 'Footer 3' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 4,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 4' },
							body: { text: 'Body 4' },
							footer: { text: 'Footer 4' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		gameConfiguration: {
			revealedCardsConfiguration: {
				numRevealSpots: 3
			}
		}
	}}
	play={async ({ canvasElement }) => {
		while (true) {
			const deck = canvasElement.querySelector('button');
			await sleep(500);
			deck!.click();
		}
	}}
/>

<Story
	name="WithThreeSortPiles"
	args={{
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 2,
				frontSide: {
					content: { image: { url: 'https://picsum.photos/200/200' } }
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 3,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 3' },
							body: { text: 'Body 3' },
							footer: { text: 'Footer 3' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 4,
				frontSide: {
					content: {
						video: { url: 'https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4' }
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 5,
				frontSide: {
					content: {
						audio: { url: 'https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4' }
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			},
			{
				id: 6,
				frontSide: {
					content: {
						audio: { url: 'https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4' }
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } }
			}
		],
		gameConfiguration: {
			sortPilesConfiguration: {
				pileNames: ['Pile 1', 'Pile 2', 'Pile 3']
			}
		}
	}}
/>
